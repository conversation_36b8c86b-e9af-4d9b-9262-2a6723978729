<?php
// Initialiser la session
session_start();

// Connexion à la base de données
require_once 'config/database.php';

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Exporter les fournisseurs vers un fichier Excel
if (isset($_GET['export_excel'])) {
    // Récupérer tous les fournisseurs
    $stmt = $conn->prepare("SELECT * FROM suppliers ORDER BY name ASC");
    $stmt->execute();
    $suppliers = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Définir les en-têtes pour le téléchargement
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename=liste_fournisseurs_' . date('Y-m-d') . '.xls');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Créer le contenu XML Excel
    echo '<?xml version="1.0" encoding="UTF-8"?>';
    echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">';
    echo '<Worksheet ss:Name="Fournisseurs">';
    echo '<Table>';
    
    // En-têtes des colonnes
    echo '<Row>';
    echo '<Cell><Data ss:Type="String">ID</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Nom</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Email</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Téléphone</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Produits fournis</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Statut</Data></Cell>';
    echo '</Row>';
    
    // Données des fournisseurs
    foreach ($suppliers as $supplier) {
        echo '<Row>';
        echo '<Cell><Data ss:Type="Number">' . $supplier['id'] . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . htmlspecialchars($supplier['name']) . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . htmlspecialchars($supplier['contact']) . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . htmlspecialchars($supplier['phone']) . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . htmlspecialchars($supplier['products']) . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . $supplier['status'] . '</Data></Cell>';
        echo '</Row>';
    }
    
    echo '</Table>';
    echo '</Worksheet>';
    echo '</Workbook>';
    exit;
}

// Rediriger vers la page des fournisseurs si aucune action n'est spécifiée
header('Location: fournisseurs.php');
exit;
?>