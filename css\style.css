/* Variables globales */
:root {
  --sidebar-width: 255px;
  --sidebar-width-collapsed: 60px;
  --primary-color: var(--bs-primary);
  --secondary-color: var(--bs-secondary);
  --success-color: var(--bs-success);
  --info-color: var(--bs-info);
  --warning-color: var(--bs-warning);
  --danger-color: var(--bs-danger);
  --dark-color: var(--bs-dark);
  --light-color: var(--bs-light);
  --transition-speed: 0.3s;
  --box-shadow: 0 5px 15px rgba(31, 37, 99, 0.875);
  --box-shadow-hover: 0 10px 20px rgba(0,0,0,0.15);
  --border-radius: 0.375rem;
}

/* Styles de base */
body {
  margin: 0;
  height: 100vh;
  overflow: hidden;
  font-family: var(--bs-font-sans-serif);
  background-color: #f8f9fa;
}

/* Structure principale */
.sidebar {
  width: var(--sidebar-width);
  height: 100vh;
  transition: width var(--transition-speed) ease;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  padding-top: 56px;
}

#main-content {
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-speed) ease;
  padding: 1.5rem;
  height: calc(100vh - 56px);
  overflow-y: auto;
  margin-top: 56px;
}

/* Navigation */
.navbar {
  position: fixed;
  width: 100%;
  z-index: 1030;
}

.sidebar .nav-link {
  padding: 0.75rem 1rem;
  color: rgba(255, 255, 255, 0.8);
  border-radius: var(--border-radius);
  margin-bottom: 0.25rem;
  transition: all var(--transition-speed) ease;
}

.sidebar .nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
  color: white;
  background-color: rgba(var(--bs-primary-rgb), 0.7);
}

.sidebar .nav-link i {
  width: 24px;
  text-align: center;
  font-size: 1.25rem;
}

/* Cartes et conteneurs */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: transform var(--transition-speed), box-shadow var(--transition-speed);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-hover);
}

.card-header {
  font-weight: 500;
  border-bottom: none;
}

.card-img-top {
  height: 200px;
  object-fit: cover;
  transition: transform var(--transition-speed) ease;
}

.card:hover .card-img-top {
  transform: scale(1.05);
}

/* Badges et étiquettes */
.badge {
  font-weight: 500;
  padding: 0.5em 0.75em;
}

.badge.rounded-circle {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Tableaux */
.table {
  margin-bottom: 0;
}

.table th {
  font-weight: 600;
  border-top: none;
}

.table-hover tbody tr:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

/* Formulaires */
.form-control, .form-select {
  border-radius: var(--border-radius);
  padding: 0.5rem 0.75rem;
  border: 1px solid #dee2e6;
}

.form-control:focus, .form-select:focus {
  border-color: var(--dark-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-dark-rgb), 0.25);
}

/* Boutons */
.btn {
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all var(--transition-speed) ease;
}

.btn-primary {
  background-color: var(--dark-color);
  border-color: var(--dark-color);
}

.btn-primary:hover {
  background-color: dark;
  border-color: rgba(8, 11, 16, 0.41);
}

.btn-outline-primary {
  color: var(--dark-color);
  border-color: var(--dark-color);
}

.btn-outline-primary:hover {
  background-color: var(--dark-color);
  border-color: var(--dark-color);
  color: white;
}

/* Animations */
.content-section {
  animation: fadeIn var(--transition-speed) ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--dark-color);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: dark;
}

/* Sections spécifiques */
#produits .product-card {
  height: 100%;
}

#achats .order-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

#fournisseurs .supplier-logo {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 50%;
}

#budget .progress {
  height: 10px;
  border-radius: 5px;
  background-color: rgba(var(--bs-dark-rgb), 0.1);
}

/* Mode responsive */
@media (max-width: 992px) {
  .sidebar {
    width: var(--sidebar-width-collapsed);
  }
  
  .sidebar .menu-text, 
  .sidebar .user-name {
    display: none;
  }
  
  .sidebar .nav-link {
    text-align: center;
    padding: 0.5rem 0;
  }
  
  .sidebar .nav-link i {
    margin-right: 0 !important;
    font-size: 1.5rem;
  }
  
  #main-content {
    margin-left: var(--sidebar-width-collapsed);
  }
}

@media (max-width: 768px) {
  .card-deck {
    display: block;
  }
  
  .card-deck .card {
    margin-bottom: 15px;
  }
  
  .display-4 {
    font-size: 2rem;
  }
  
  .table-responsive {
    border: none;
  }
}

/* Thème sombre pour la sidebar */
.sidebar {
  background-color: #212529;
  color: white;
}

/* Styles pour les statistiques */
.stat-card {
  text-align: center;
  padding: 1.5rem;
}

.stat-card .stat-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  font-size: 0.875rem;
  color: dark;
}

/* Code d'exemple */
.code-example {
  background-color: #f8f9fa;
  border-radius: var(--border-radius);
  padding: 1rem;
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 0.875rem;
  color: dark;
  margin: 1rem 0;
  white-space: pre;
  overflow-x: auto;
}

/* Styles pour les notifications */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Styles pour les tooltips */
.custom-tooltip {
  position: relative;
}

.custom-tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: 1000;
}

