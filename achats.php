<?php
// Initialiser la session
session_start();

// Connexion à la base de données
require_once 'config/database.php';

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Traiter l'export Excel
if (isset($_GET['export_excel'])) {
    // Récupérer toutes les commandes avec les détails
    $stmt = $conn->prepare("SELECT o.*, p.name as product_name, p.price, s.name as supplier_name
                            FROM orders o
                            JOIN products p ON o.product_id = p.id
                            JOIN suppliers s ON o.supplier_id = s.id
                            ORDER BY o.order_date DESC");
    $stmt->execute();
    $orders = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

    // Définir les en-têtes pour le téléchargement
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename=commandes_' . date('Y-m-d') . '.xls');
    header('Pragma: no-cache');
    header('Expires: 0');

    // Créer le contenu Excel
    echo '<?xml version="1.0"?>';
    echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet">';
    echo '<Worksheet ss:Name="Commandes">';
    echo '<Table>';

    // En-têtes
    echo '<Row>';
    echo '<Cell><Data ss:Type="String">Référence</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Produit</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Quantité</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Fournisseur</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Date commande</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Date livraison</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Montant</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Statut</Data></Cell>';
    echo '<Cell><Data ss:Type="String">Commentaires</Data></Cell>';
    echo '</Row>';

    // Données des commandes
    foreach ($orders as $order) {
        echo '<Row>';
        echo '<Cell><Data ss:Type="String">' . htmlspecialchars($order['reference']) . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . htmlspecialchars($order['product_name']) . '</Data></Cell>';
        echo '<Cell><Data ss:Type="Number">' . $order['quantity'] . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . htmlspecialchars($order['supplier_name']) . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . date('d/m/Y', strtotime($order['order_date'])) . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . date('d/m/Y', strtotime($order['delivery_date'])) . '</Data></Cell>';
        echo '<Cell><Data ss:Type="Number">' . $order['amount'] . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . $order['status'] . '</Data></Cell>';
        echo '<Cell><Data ss:Type="String">' . htmlspecialchars($order['comments']) . '</Data></Cell>';
        echo '</Row>';
    }

    echo '</Table>';
    echo '</Worksheet>';
    echo '</Workbook>';
    exit;
}

// Récupérer les commandes
$stmt = $conn->prepare("SELECT o.*, p.name as product_name, p.price, s.name as supplier_name 
                        FROM orders o 
                        JOIN products p ON o.product_id = p.id 
                        JOIN suppliers s ON o.supplier_id = s.id 
                        ORDER BY o.order_date DESC");
$stmt->execute();
$orders = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Récupérer les produits pour le formulaire
$stmt = $conn->prepare("SELECT id, name, price, stock FROM products WHERE stock > 0 ORDER BY name ASC");
$stmt->execute();
$products = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Récupérer les fournisseurs pour le formulaire
$stmt = $conn->prepare("SELECT id, name FROM suppliers WHERE status = 'Actif' ORDER BY name ASC");
$stmt->execute();
$suppliers = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Récupérer les statistiques
$stats = [];

// Total des commandes
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM orders");
$stmt->execute();
$stats['total'] = $stmt->get_result()->fetch_assoc()['total'];

// Commandes en cours
$stmt = $conn->prepare("SELECT COUNT(*) as en_cours FROM orders WHERE status = 'En cours'");
$stmt->execute();
$stats['en_cours'] = $stmt->get_result()->fetch_assoc()['en_cours'];

// Commandes livrées
$stmt = $conn->prepare("SELECT COUNT(*) as livrees FROM orders WHERE status = 'Livré'");
$stmt->execute();
$stats['livrees'] = $stmt->get_result()->fetch_assoc()['livrees'];

// Commandes annulées
$stmt = $conn->prepare("SELECT COUNT(*) as annulees FROM orders WHERE status = 'Annulé'");
$stmt->execute();
$stats['annulees'] = $stmt->get_result()->fetch_assoc()['annulees'];

// Montant total des commandes
$stmt = $conn->prepare("SELECT SUM(amount) as total_amount FROM orders WHERE status != 'Annulé'");
$stmt->execute();
$stats['total_amount'] = $stmt->get_result()->fetch_assoc()['total_amount'] ?? 0;

// Commandes du mois en cours
$stmt = $conn->prepare("SELECT COUNT(*) as ce_mois FROM orders WHERE MONTH(order_date) = MONTH(CURRENT_DATE()) AND YEAR(order_date) = YEAR(CURRENT_DATE())");
$stmt->execute();
$stats['ce_mois'] = $stmt->get_result()->fetch_assoc()['ce_mois'];

// Traiter la création d'une nouvelle commande
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_order'])) {
    $product_id = $_POST['product_id'];
    $quantity = $_POST['quantity'];
    $supplier_id = $_POST['supplier_id'];
    $delivery_date = $_POST['delivery_date'];
    $comments = $_POST['comments'] ?? '';
    
    // Validation des données
    if (empty($product_id) || empty($quantity) || empty($supplier_id) || empty($delivery_date)) {
        $_SESSION['error_message'] = "Tous les champs obligatoires doivent être remplis";
    } elseif (!is_numeric($quantity) || $quantity <= 0) {
        $_SESSION['error_message'] = "La quantité doit être un nombre positif";
    } elseif (strtotime($delivery_date) < strtotime(date('Y-m-d'))) {
        $_SESSION['error_message'] = "La date de livraison doit être dans le futur";
    } else {
        // Générer une référence de commande
        $year = date('Y');
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE YEAR(order_date) = ?");
        $stmt->bind_param("i", $year);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        $count = $result['count'] + 1;
        $reference = "CMD-" . $year . "-" . str_pad($count, 3, "0", STR_PAD_LEFT);
        
        // Récupérer le prix et le stock du produit
        $stmt = $conn->prepare("SELECT price, stock FROM products WHERE id = ?");
        $stmt->bind_param("i", $product_id);
        $stmt->execute();
        $product = $stmt->get_result()->fetch_assoc();

        if (!$product) {
            $_SESSION['error_message'] = "Produit non trouvé";
        } elseif ($product['stock'] < $quantity) {
            $_SESSION['error_message'] = "Stock insuffisant. Stock disponible: " . $product['stock'];
        } else {
            $price = $product['price'];

            // Calculer le montant total
            $amount = $price * $quantity;

            // Insérer la commande
            $stmt = $conn->prepare("INSERT INTO orders (reference, product_id, quantity, supplier_id, order_date, delivery_date, amount, status, comments)
                                    VALUES (?, ?, ?, ?, NOW(), ?, ?, 'En cours', ?)");
            $stmt->bind_param("siisdds", $reference, $product_id, $quantity, $supplier_id, $delivery_date, $amount, $comments);

            if ($stmt->execute()) {
                // Mettre à jour le stock du produit
                $stmt = $conn->prepare("UPDATE products SET stock = stock - ? WHERE id = ?");
                $stmt->bind_param("ii", $quantity, $product_id);
                $stmt->execute();

                $_SESSION['success_message'] = "Commande créée avec succès";
                header('Location: achats.php');
                exit;
            } else {
                $_SESSION['error_message'] = "Erreur lors de la création de la commande: " . $conn->error;
            }
        }
    }
}

// Traiter la mise à jour du statut d'une commande
if (isset($_GET['complete']) && is_numeric($_GET['complete'])) {
    $id = $_GET['complete'];
    
    $stmt = $conn->prepare("UPDATE orders SET status = 'Livré' WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        $_SESSION['success_message'] = "Commande marquée comme livrée";
    } else {
        $_SESSION['error_message'] = "Erreur lors de la mise à jour du statut: " . $conn->error;
    }
    
    header('Location: achats.php');
    exit;
}

// Traiter l'annulation d'une commande
if (isset($_GET['cancel']) && is_numeric($_GET['cancel'])) {
    $id = $_GET['cancel'];
    
    // Récupérer les informations de la commande
    $stmt = $conn->prepare("SELECT product_id, quantity FROM orders WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $order = $stmt->get_result()->fetch_assoc();
    
    // Mettre à jour le statut
    $stmt = $conn->prepare("UPDATE orders SET status = 'Annulé' WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        // Remettre les produits en stock
        $stmt = $conn->prepare("UPDATE products SET stock = stock + ? WHERE id = ?");
        $stmt->bind_param("ii", $order['quantity'], $order['product_id']);
        $stmt->execute();
        
        $_SESSION['success_message'] = "Commande annulée avec succès";
    } else {
        $_SESSION['error_message'] = "Erreur lors de l'annulation de la commande: " . $conn->error;
    }
    
    header('Location: achats.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Achats - Application Gestion des Achats</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <?php include 'includes/navbar.php'; ?>

  <div class="d-flex">
    <?php include 'includes/sidebar.php'; ?>

    <!-- Contenu principal -->
    <div class="p-4 flex-grow-1" id="main-content">
      <div id="achats" class="content-section">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h3>Gestion des achats</h3>
          <div class="d-flex gap-2">
            <a href="achats.php?export_excel=1" class="btn btn-success" title="Exporter vers Excel">
              <i class="bi bi-file-earmark-excel me-2"></i>Exporter Excel
            </a>
            <button type="button" class="btn btn-dark" data-bs-toggle="modal" data-bs-target="#createOrderModal">
              <i class="bi bi-cart-plus me-2"></i>Créer un bon de commande
            </button>
          </div>
        </div>
        
        <?php if (isset($_SESSION['success_message'])): ?>
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error_message'])): ?>
          <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        <?php endif; ?>

        <!-- Statistiques -->
        <div class="row mb-4">
          <div class="col-md-2">
            <div class="card border-0 shadow-sm text-center">
              <div class="card-body">
                <i class="bi bi-cart3 text-primary fs-1"></i>
                <h4 class="mt-2 mb-0"><?php echo $stats['total']; ?></h4>
                <small class="text-muted">Total commandes</small>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="card border-0 shadow-sm text-center">
              <div class="card-body">
                <i class="bi bi-clock text-warning fs-1"></i>
                <h4 class="mt-2 mb-0"><?php echo $stats['en_cours']; ?></h4>
                <small class="text-muted">En cours</small>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="card border-0 shadow-sm text-center">
              <div class="card-body">
                <i class="bi bi-check-circle text-success fs-1"></i>
                <h4 class="mt-2 mb-0"><?php echo $stats['livrees']; ?></h4>
                <small class="text-muted">Livrées</small>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="card border-0 shadow-sm text-center">
              <div class="card-body">
                <i class="bi bi-x-circle text-danger fs-1"></i>
                <h4 class="mt-2 mb-0"><?php echo $stats['annulees']; ?></h4>
                <small class="text-muted">Annulées</small>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="card border-0 shadow-sm text-center">
              <div class="card-body">
                <i class="bi bi-currency-euro text-info fs-1"></i>
                <h4 class="mt-2 mb-0"><?php echo number_format($stats['total_amount'], 0); ?> €</h4>
                <small class="text-muted">Montant total</small>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="card border-0 shadow-sm text-center">
              <div class="card-body">
                <i class="bi bi-calendar-month text-secondary fs-1"></i>
                <h4 class="mt-2 mb-0"><?php echo $stats['ce_mois']; ?></h4>
                <small class="text-muted">Ce mois</small>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Filtres et recherche -->
        <div class="card border-0 shadow-sm mb-4">
          <div class="card-body">
            <div class="row g-2">
              <div class="col-md-3">
                <div class="input-group">
                  <span class="input-group-text bg-light"><i class="bi bi-search"></i></span>
                  <input type="text" id="searchInput" class="form-control" placeholder="Rechercher...">
                </div>
              </div>
              <div class="col-md-3">
                <select id="statusFilter" class="form-select">
                  <option value="all">Tous les statuts</option>
                  <option value="En cours">En cours</option>
                  <option value="Livré">Livré</option>
                  <option value="Annulé">Annulé</option>
                </select>
              </div>
              <div class="col-md-3">
                <select id="supplierFilter" class="form-select">
                  <option value="all">Tous les fournisseurs</option>
                  <?php foreach ($suppliers as $supplier): ?>
                  <option value="<?php echo htmlspecialchars($supplier['name']); ?>"><?php echo htmlspecialchars($supplier['name']); ?></option>
                  <?php endforeach; ?>
                </select>
              </div>
              <div class="col-md-3">
                <select id="dateFilter" class="form-select">
                  <option value="all">Toutes les dates</option>
                  <option value="today">Aujourd'hui</option>
                  <option value="week">Cette semaine</option>
                  <option value="month">Ce mois</option>
                  <option value="year">Cette année</option>
                </select>
              </div>
            </div>
            <div class="row g-2 mt-2">
              <div class="col-md-6">
                <div class="input-group">
                  <span class="input-group-text bg-light"><i class="bi bi-calendar"></i></span>
                  <input type="date" id="dateFrom" class="form-control" placeholder="Date de début">
                  <span class="input-group-text bg-light">à</span>
                  <input type="date" id="dateTo" class="form-control" placeholder="Date de fin">
                </div>
              </div>
              <div class="col-md-6">
                <button type="button" id="resetFilters" class="btn btn-outline-secondary">
                  <i class="bi bi-arrow-clockwise me-1"></i>Réinitialiser les filtres
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Liste des commandes -->
        <div class="card border-0 shadow-sm">
          <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Liste des commandes</h5>
            <span class="badge bg-light text-dark"><?php echo count($orders); ?> commandes</span>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>Référence</th>
                    <th>Produit</th>
                    <th>Fournisseur</th>
                    <th>Date</th>
                    <th>Montant</th>
                    <th>Statut</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($orders as $order): ?>
                  <tr class="order-row"
                      data-status="<?php echo $order['status']; ?>"
                      data-supplier="<?php echo htmlspecialchars($order['supplier_name']); ?>"
                      data-date="<?php echo $order['order_date']; ?>"
                      data-search="<?php echo strtolower($order['reference'] . ' ' . $order['product_name'] . ' ' . $order['supplier_name']); ?>">
                    <td><small class="text-muted"><?php echo $order['reference']; ?></small></td>
                    <td><?php echo $order['product_name']; ?> (<?php echo $order['quantity']; ?>)</td>
                    <td><?php echo $order['supplier_name']; ?></td>
                    <td><?php echo date('d/m/Y', strtotime($order['order_date'])); ?></td>
                    <td><?php echo number_format($order['amount'], 2); ?> €</td>
                    <td><span class="badge <?php echo $order['status'] === 'Livré' ? 'bg-success' : ($order['status'] === 'Annulé' ? 'bg-danger' : 'bg-warning'); ?>"><?php echo $order['status']; ?></span></td>
                    <td>
                      <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary view-order" data-bs-toggle="modal" data-bs-target="#viewOrderModal" 
                                data-id="<?php echo $order['id']; ?>"
                                data-reference="<?php echo $order['reference']; ?>"
                                data-product="<?php echo $order['product_name']; ?>"
                                data-quantity="<?php echo $order['quantity']; ?>"
                                data-supplier="<?php echo $order['supplier_name']; ?>"
                                data-date="<?php echo date('d/m/Y', strtotime($order['order_date'])); ?>"
                                data-delivery="<?php echo date('d/m/Y', strtotime($order['delivery_date'])); ?>"
                                data-amount="<?php echo number_format($order['amount'], 2); ?>"
                                data-status="<?php echo $order['status']; ?>"
                                data-comments="<?php echo htmlspecialchars($order['comments']); ?>">
                          <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="window.print()" title="Imprimer">
                          <i class="bi bi-printer"></i>
                        </button>
                        <?php if ($order['status'] === 'En cours'): ?>
                        <a href="achats.php?complete=<?php echo $order['id']; ?>" class="btn btn-outline-success" onclick="return confirm('Marquer cette commande comme livrée?')">
                          <i class="bi bi-check-lg"></i>
                        </a>
                        <a href="achats.php?cancel=<?php echo $order['id']; ?>" class="btn btn-outline-danger" onclick="return confirm('Êtes-vous sûr de vouloir annuler cette commande?')">
                          <i class="bi bi-x-lg"></i>
                        </a>
                        <?php endif; ?>
                      </div>
                    </td>
                  </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          </div>
          <?php if (count($orders) > 10): ?>
          <div class="card-footer bg-light">
            <nav aria-label="Page navigation">
              <ul class="pagination justify-content-center mb-0">
                <li class="page-item disabled">
                  <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Précédent</a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item">
                  <a class="page-link" href="#">Suivant</a>
                </li>
              </ul>
            </nav>
          </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal pour créer un bon de commande -->
  <div class="modal fade" id="createOrderModal" tabindex="-1" aria-labelledby="createOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-dark text-white">
          <h5 class="modal-title" id="createOrderModalLabel">Créer un bon de commande</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form method="POST" action="achats.php">
            <div class="mb-3">
              <label class="form-label">Produit</label>
              <select class="form-select" name="product_id" required>
                <option value="" selected disabled>Sélectionner un produit</option>
                <?php foreach ($products as $product): ?>
                <option value="<?php echo $product['id']; ?>" data-stock="<?php echo $product['stock']; ?>"><?php echo $product['name']; ?> (<?php echo $product['price']; ?> € - Stock: <?php echo $product['stock']; ?>)</option>
                <?php endforeach; ?>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Quantité</label>
              <input type="number" class="form-control" name="quantity" min="1" value="1" required>
            </div>
            <div class="mb-3">
              <label class="form-label">Fournisseur</label>
              <select class="form-select" name="supplier_id" required>
                <option value="" selected disabled>Sélectionner un fournisseur</option>
                <?php foreach ($suppliers as $supplier): ?>
                <option value="<?php echo $supplier['id']; ?>"><?php echo $supplier['name']; ?></option>
                <?php endforeach; ?>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Date de livraison souhaitée</label>
              <input type="date" class="form-control" name="delivery_date" required>
            </div>
            <div class="mb-3">
              <label class="form-label">Commentaires</label>
              <textarea class="form-control" name="comments" rows="2"></textarea>
            </div>
            <div class="text-end">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
              <button type="submit" name="create_order" class="btn btn-success">
                <i class="bi bi-plus-circle me-2"></i>Créer la commande
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Modal pour voir les détails d'une commande -->
  <div class="modal fade" id="viewOrderModal" tabindex="-1" aria-labelledby="viewOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-dark text-white">
          <h5 class="modal-title" id="viewOrderModalLabel">Détails de la commande</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row mb-3">
            <div class="col-md-6">
              <p class="mb-1"><strong>Référence:</strong> <span id="orderReference"></span></p>
              <p class="mb-1"><strong>Produit:</strong> <span id="orderProduct"></span></p>
              <p class="mb-1"><strong>Quantité:</strong> <span id="orderQuantity"></span></p>
              <p class="mb-1"><strong>Fournisseur:</strong> <span id="orderSupplier"></span></p>
            </div>
            <div class="col-md-6">
              <p class="mb-1"><strong>Date de commande:</strong> <span id="orderDate"></span></p>
              <p class="mb-1"><strong>Date de livraison:</strong> <span id="orderDelivery"></span></p>
              <p class="mb-1"><strong>Montant:</strong> <span id="orderAmount"></span> €</p>
              <p class="mb-1"><strong>Statut:</strong> <span id="orderStatus" class="badge"></span></p>
            </div>
          </div>
          <div class="mb-3">
            <label class="form-label">Commentaires:</label>
            <textarea class="form-control" id="orderComments" rows="3" readonly></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Script pour les fonctionnalités JavaScript -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialiser les tooltips Bootstrap
      var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
      var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
      });
      
      // Fonction pour appliquer tous les filtres
      function applyFilters() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const supplierFilter = document.getElementById('supplierFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;
        const dateFrom = document.getElementById('dateFrom').value;
        const dateTo = document.getElementById('dateTo').value;
        const rows = document.querySelectorAll('.order-row');

        rows.forEach(row => {
          let show = true;

          // Filtre de recherche
          if (searchTerm && !row.getAttribute('data-search').includes(searchTerm)) {
            show = false;
          }

          // Filtre par statut
          if (statusFilter !== 'all' && row.getAttribute('data-status') !== statusFilter) {
            show = false;
          }

          // Filtre par fournisseur
          if (supplierFilter !== 'all' && row.getAttribute('data-supplier') !== supplierFilter) {
            show = false;
          }

          // Filtre par date
          const orderDate = new Date(row.getAttribute('data-date'));
          const today = new Date();

          if (dateFilter !== 'all') {
            switch (dateFilter) {
              case 'today':
                if (orderDate.toDateString() !== today.toDateString()) show = false;
                break;
              case 'week':
                const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                if (orderDate < weekAgo) show = false;
                break;
              case 'month':
                if (orderDate.getMonth() !== today.getMonth() || orderDate.getFullYear() !== today.getFullYear()) show = false;
                break;
              case 'year':
                if (orderDate.getFullYear() !== today.getFullYear()) show = false;
                break;
            }
          }

          // Filtre par plage de dates
          if (dateFrom && orderDate < new Date(dateFrom)) show = false;
          if (dateTo && orderDate > new Date(dateTo)) show = false;

          row.style.display = show ? '' : 'none';
        });
      }

      // Événements pour tous les filtres
      document.getElementById('searchInput').addEventListener('keyup', applyFilters);
      document.getElementById('statusFilter').addEventListener('change', applyFilters);
      document.getElementById('supplierFilter').addEventListener('change', applyFilters);
      document.getElementById('dateFilter').addEventListener('change', applyFilters);
      document.getElementById('dateFrom').addEventListener('change', applyFilters);
      document.getElementById('dateTo').addEventListener('change', applyFilters);

      // Réinitialiser les filtres
      document.getElementById('resetFilters').addEventListener('click', function() {
        document.getElementById('searchInput').value = '';
        document.getElementById('statusFilter').value = 'all';
        document.getElementById('supplierFilter').value = 'all';
        document.getElementById('dateFilter').value = 'all';
        document.getElementById('dateFrom').value = '';
        document.getElementById('dateTo').value = '';
        applyFilters();
      });
      
      // Afficher les détails de la commande dans la modal
      const viewOrderButtons = document.querySelectorAll('.view-order');
      viewOrderButtons.forEach(button => {
        button.addEventListener('click', function() {
          document.getElementById('orderReference').textContent = this.getAttribute('data-reference');
          document.getElementById('orderProduct').textContent = this.getAttribute('data-product');
          document.getElementById('orderQuantity').textContent = this.getAttribute('data-quantity');
          document.getElementById('orderSupplier').textContent = this.getAttribute('data-supplier');
          document.getElementById('orderDate').textContent = this.getAttribute('data-date');
          document.getElementById('orderDelivery').textContent = this.getAttribute('data-delivery');
          document.getElementById('orderAmount').textContent = this.getAttribute('data-amount');
          
          const status = this.getAttribute('data-status');
          const statusElement = document.getElementById('orderStatus');
          statusElement.textContent = status;
          
          // Définir la classe de couleur en fonction du statut
          statusElement.className = 'badge';
          if (status === 'Livré') {
            statusElement.classList.add('bg-success');
          } else if (status === 'Annulé') {
            statusElement.classList.add('bg-danger');
          } else {
            statusElement.classList.add('bg-warning');
          }
          
          document.getElementById('orderComments').textContent = this.getAttribute('data-comments') || 'Aucun commentaire';
        });
      });
      
      // Définir la date de livraison par défaut à 7 jours plus tard et la date minimale à demain
      const deliveryDateInput = document.querySelector('input[name="delivery_date"]');
      if (deliveryDateInput) {
        const today = new Date();
        const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
        const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

        // Définir la date minimale à demain
        deliveryDateInput.min = tomorrow.toISOString().split('T')[0];

        // Définir la valeur par défaut à 7 jours plus tard
        const formattedDate = nextWeek.toISOString().split('T')[0];
        deliveryDateInput.value = formattedDate;
      }

      // Validation de la quantité en fonction du stock
      const productSelect = document.querySelector('select[name="product_id"]');
      const quantityInput = document.querySelector('input[name="quantity"]');

      if (productSelect && quantityInput) {
        productSelect.addEventListener('change', function() {
          const selectedOption = this.options[this.selectedIndex];
          const stock = selectedOption.getAttribute('data-stock');
          if (stock) {
            quantityInput.max = stock;
            quantityInput.title = `Stock disponible: ${stock}`;
            if (parseInt(quantityInput.value) > parseInt(stock)) {
              quantityInput.value = stock;
            }
          }
        });

        quantityInput.addEventListener('input', function() {
          const selectedOption = productSelect.options[productSelect.selectedIndex];
          const stock = selectedOption.getAttribute('data-stock');
          if (stock && parseInt(this.value) > parseInt(stock)) {
            this.value = stock;
            alert(`La quantité ne peut pas dépasser le stock disponible (${stock})`);
          }
        });
      }
    });
  </script>
</body>
</html>

