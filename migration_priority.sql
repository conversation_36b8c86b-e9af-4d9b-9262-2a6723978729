-- Migration pour ajouter la priorité aux commandes
-- Date: 2025-07-12

USE gestion_achats;

-- Ajouter la colonne priority à la table orders
ALTER TABLE orders 
ADD COLUMN priority ENUM('Basse', 'Normale', 'Haute', 'Urgente') NOT NULL DEFAULT 'Normale' 
AFTER status;

-- Mettre à jour quelques commandes existantes avec des priorités différentes pour les tests
UPDATE orders SET priority = 'Haute' WHERE status = 'En cours' LIMIT 1;
UPDATE orders SET priority = 'Urgente' WHERE reference LIKE 'CMD-%-001';
UPDATE orders SET priority = 'Basse' WHERE status = 'Livré' LIMIT 1;

-- Afficher les commandes avec leur nouvelle priorité
SELECT reference, status, priority, order_date 
FROM orders 
ORDER BY 
  CASE priority 
    WHEN 'Urgente' THEN 1 
    WHEN 'Haute' THEN 2 
    WHEN 'Normale' THEN 3 
    WHEN 'Basse' THEN 4 
  END, 
  order_date DESC;
