-- Création de la base de données
CREATE DATABASE gestion_achats;
USE gestion_achats;

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  email VARCHAR(100) NOT NULL UNIQUE,
  full_name VARCHAR(100) NOT NULL,
  role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des fournisseurs
CREATE TABLE IF NOT EXISTS suppliers (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  contact VARCHAR(100) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  products TEXT,
  status ENUM('Actif', 'Inactif') NOT NULL DEFAULT 'Actif',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des produits
CREATE TABLE IF NOT EXISTS products (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  category VARCHAR(50) NOT NULL,
  stock INT NOT NULL DEFAULT 0,
  supplier_id INT,
  image VARCHAR(255) DEFAULT 'default.jpg',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL
);

-- Table des commandes
CREATE TABLE IF NOT EXISTS orders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  reference VARCHAR(20) NOT NULL UNIQUE,
  product_id INT NOT NULL,
  quantity INT NOT NULL,
  supplier_id INT NOT NULL,
  order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  delivery_date DATE,
  amount DECIMAL(10,2) NOT NULL,
  status ENUM('En cours', 'Livré', 'Annulé') NOT NULL DEFAULT 'En cours',
  comments TEXT,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
  FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE
);

-- Insertion d'un utilisateur admin (mot de passe: admin123)
INSERT INTO users (username, password, email, full_name, role) VALUES
('admin', '$2y$10$8MNE5ERJJl.P9TLBVUXnUOBCwQUit56LLqaB9PtXJpJfJFZYrm3Hy', '<EMAIL>', 'Administrateur', 'admin');

-- Insertion de quelques fournisseurs
INSERT INTO suppliers (name, contact, phone, products, status) VALUES
('Fournisseur A', '<EMAIL>', '0123456789', 'Électronique, Informatique', 'Actif'),
('Fournisseur B', '<EMAIL>', '0987654321', 'Papeterie, Fournitures de bureau', 'Actif'),
('Fournisseur C', '<EMAIL>', '0567891234', 'Mobilier, Décoration', 'Actif');

-- Insertion de quelques produits
INSERT INTO products (name, price, category, stock, supplier_id, image) VALUES
('Ordinateur portable', 899.99, 'Informatique', 15, 1, 'laptop.jpg'),
('Imprimante laser', 299.99, 'Informatique', 8, 1, 'printer.jpg'),
('Bureau ergonomique', 249.99, 'Mobilier', 5, 3, 'desk.jpg'),
('Chaise de bureau', 129.99, 'Mobilier', 12, 3, 'chair.jpg'),
('Ramette papier A4', 4.99, 'Papeterie', 50, 2, 'paper.jpg'),
('Stylos (lot de 10)', 7.99, 'Papeterie', 30, 2, 'pens.jpg');

-- Insertion de quelques commandes
INSERT INTO orders (reference, product_id, quantity, supplier_id, delivery_date, amount, status) VALUES
('CMD-2023-001', 1, 2, 1, DATE_ADD(CURRENT_DATE, INTERVAL 7 DAY), 1799.98, 'En cours'),
('CMD-2023-002', 3, 3, 3, DATE_ADD(CURRENT_DATE, INTERVAL 14 DAY), 749.97, 'En cours'),
('CMD-2023-003', 5, 10, 2, DATE_ADD(CURRENT_DATE, INTERVAL -7 DAY), 49.90, 'Livré');