/* Correction de la classe d-none mal formatée */
.content-section.d-none {
  display: none;
}

/* Optimisation des styles */
body {
  margin: 0;
  height: 100vh;
  overflow: hidden;
  font-family: var(--bs-font-sans-serif);
}

.sidebar {
  width: 255px;
  height: 100vh;
}

/* Utilisation des variables CSS de Bootstrap */
.content-section {
  font-size: 1rem;
  padding: 1rem;
}

/* Améliorations visuelles */
.card {
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}

.card-img-top {
  height: 200px;
  object-fit: cover;
}

.card-text.text-muted {
  font-weight: bold;
  font-size: 1.1rem;
  color: var(--bs-primary) !important;
}

/* Ajout de scrollbar pour la section produits */
#produits {
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  padding-right: 10px;
}

/* Style de la scrollbar (pour navigateurs modernes) */
#produits::-webkit-scrollbar {
  width: 8px;
}

#produits::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

#produits::-webkit-scrollbar-thumb {
  background: var(--bs-primary);
  border-radius: 10px;
}

#produits::-webkit-scrollbar-thumb:hover {
  background: #0056b3;
}

/* Styles pour la page d'accueil */
#accueil .card {
  transition: transform 0.2s;
}

#accueil .card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1) !important;
}

#accueil .lead {
  font-size: 1.1rem;
  color: #6c757d;
}

#accueil h2, #accueil h3, #accueil h4 {
  color: #333;
  font-weight: 600;
}

#accueil .list-group-item-action:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

#accueil .text-success, #accueil .text-warning {
  font-weight: 500;
}

/* Mode compact pour la sidebar */
@media (max-width: 992px) {
  .sidebar {
    width: 60px;
  }
  
  .sidebar .menu-text, 
  .sidebar .user-name {
    display: none;
    font-size: 1.5.rem;
  }
  
  .sidebar .nav-link {
    text-align: center;
    padding: 0.5rem 0;
  }
  
  .sidebar .nav-link i {
    margin-right: 0 !important;
    font-size: 1.7rem;
  }
  
  .sidebar .mt-auto {
    text-align: center;
  }
}

/* Ajustement du contenu principal */
@media (max-width: 992px) {
  #main-content {
    margin-left: 60px;
  }
}

/* Styles pour les sections spécifiques */
#fournisseurs .table-responsive {
  max-height: 400px;
  overflow-y: auto;
}

#budget .display-4 {
  font-size: 2.5rem;
  font-weight: 600;
}

#budget .progress {
  height: 10px;
  border-radius: 5px;
}

#reports .badge {
  font-size: 1rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

/* Styles communs pour toutes les sections */
.card-header {
  font-weight: 500;
}

.content-section {
  animation: fadeIn 0.3s ease-in-out;
}

/* @keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
} */
