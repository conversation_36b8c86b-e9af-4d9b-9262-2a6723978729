<?xml version="1.0"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Version>16.00</Version>
 </DocumentProperties>
 <OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office">
  <AllowPNG/>
 </OfficeDocumentSettings>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>8628</WindowHeight>
  <WindowWidth>23040</WindowWidth>
  <WindowTopX>32767</WindowTopX>
  <WindowTopY>32767</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s62" ss:Name="Lien hypertexte">
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#0563C1"
    ss:Underline="Single"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="Fournisseurs">
  <Table ss:ExpandedColumnCount="6" ss:ExpandedRowCount="8" x:FullColumns="1"
   x:FullRows="1" ss:DefaultColumnWidth="62.400000000000006"
   ss:DefaultRowHeight="14.4">
   <Column ss:Index="3" ss:AutoFitWidth="0" ss:Width="207.6"/>
   <Row>
    <Cell><Data ss:Type="String">ID</Data></Cell>
    <Cell><Data ss:Type="String">Nom</Data></Cell>
    <Cell><Data ss:Type="String">Email</Data></Cell>
    <Cell><Data ss:Type="String">Téléphone</Data></Cell>
    <Cell><Data ss:Type="String">Produits fournis</Data></Cell>
    <Cell><Data ss:Type="String">Statut</Data></Cell>
   </Row>
   <Row>
    <Cell><Data ss:Type="Number">1</Data></Cell>
    <Cell><Data ss:Type="String">Fournisseur A</Data></Cell>
    <Cell><Data ss:Type="String"><EMAIL></Data></Cell>
    <Cell><Data ss:Type="String">0123456789</Data></Cell>
    <Cell><Data ss:Type="String">Électronique, Informatique</Data></Cell>
    <Cell><Data ss:Type="String">Actif</Data></Cell>
   </Row>
   <Row>
    <Cell><Data ss:Type="Number">2</Data></Cell>
    <Cell><Data ss:Type="String">Fournisseur B</Data></Cell>
    <Cell><Data ss:Type="String"><EMAIL></Data></Cell>
    <Cell><Data ss:Type="String">0987654321</Data></Cell>
    <Cell><Data ss:Type="String">Papeterie, Fournitures de bureau</Data></Cell>
    <Cell><Data ss:Type="String">Actif</Data></Cell>
   </Row>
   <Row>
    <Cell><Data ss:Type="Number">3</Data></Cell>
    <Cell><Data ss:Type="String">Fournisseur C</Data></Cell>
    <Cell><Data ss:Type="String"><EMAIL></Data></Cell>
    <Cell><Data ss:Type="String">0567891234</Data></Cell>
    <Cell><Data ss:Type="String">Mobilier, Décoration</Data></Cell>
    <Cell><Data ss:Type="String">Inactif</Data></Cell>
   </Row>
   <Row>
    <Cell><Data ss:Type="Number">6</Data></Cell>
    <Cell><Data ss:Type="String">Fournisseur D</Data></Cell>
    <Cell><Data ss:Type="String"><EMAIL></Data></Cell>
    <Cell><Data ss:Type="String">34345332</Data></Cell>
    <Cell><Data ss:Type="String">pepeterie</Data></Cell>
    <Cell><Data ss:Type="String">Actif</Data></Cell>
   </Row>
   <Row>
    <Cell><Data ss:Type="Number">7</Data></Cell>
    <Cell><Data ss:Type="String">fournisseur E</Data></Cell>
    <Cell><Data ss:Type="String"><EMAIL></Data></Cell>
    <Cell><Data ss:Type="String">34341211</Data></Cell>
    <Cell><Data ss:Type="String">Mobilier</Data></Cell>
    <Cell><Data ss:Type="String">Actif</Data></Cell>
   </Row>
   <Row>
    <Cell><Data ss:Type="Number">8</Data></Cell>
    <Cell><Data ss:Type="String">Makran</Data></Cell>
    <Cell><Data ss:Type="String"><EMAIL></Data></Cell>
    <Cell><Data ss:Type="String">34341211</Data></Cell>
    <Cell><Data ss:Type="String">Electronique</Data></Cell>
    <Cell><Data ss:Type="String">Actif</Data></Cell>
   </Row>
   <Row>
    <Cell><Data ss:Type="Number">9</Data></Cell>
    <Cell><Data ss:Type="String">siditaher</Data></Cell>
    <Cell ss:StyleID="s62" ss:HRef="mailto:<EMAIL>"><Data
      ss:Type="String"><EMAIL></Data></Cell>
    <Cell><Data ss:Type="Number">49141433</Data></Cell>
    <Cell><Data ss:Type="String">developper</Data></Cell>
    <Cell><Data ss:Type="String">Actif</Data></Cell>
   </Row>
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <Header x:Margin="0.4921259845"/>
    <Footer x:Margin="0.4921259845"/>
    <PageMargins x:Bottom="0.984251969" x:Left="0.78740157499999996"
     x:Right="0.78740157499999996" x:Top="0.984251969"/>
   </PageSetup>
   <Selected/>
   <Panes>
    <Pane>
     <Number>3</Number>
     <ActiveRow>8</ActiveRow>
     <ActiveCol>5</ActiveCol>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
</Workbook>
