<?php
// Initialiser la session
session_start();

// Connexion à la base de données
require_once 'config/database.php';

// Vérifier si l'utilisateur est connecté et est admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// Récupérer tous les utilisateurs
$stmt = $conn->prepare("SELECT id, username, email, full_name, role, created_at FROM users ORDER BY id ASC");
$stmt->execute();
$users = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Traiter l'ajout d'un nouvel utilisateur
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_user'])) {
    $username = $_POST['username'];
    $password = $_POST['password'];
    $email = $_POST['email'];
    $full_name = $_POST['full_name'];
    $role = $_POST['role'];
    
    // Vérifier si le nom d'utilisateur existe déjà
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $_SESSION['error_message'] = "Ce nom d'utilisateur est déjà utilisé";
    } else {
        // Vérifier si l'email existe déjà
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $_SESSION['error_message'] = "Cet email est déjà utilisé";
        } else {
            // Hachage du mot de passe
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // Insertion de l'utilisateur
            $stmt = $conn->prepare("INSERT INTO users (username, password, email, full_name, role) VALUES (?, ?, ?, ?, ?)");
            $stmt->bind_param("sssss", $username, $hashed_password, $email, $full_name, $role);
            
            if ($stmt->execute()) {
                $_SESSION['success_message'] = "Utilisateur ajouté avec succès";
                header('Location: users.php');
                exit;
            } else {
                $_SESSION['error_message'] = "Erreur lors de l'ajout de l'utilisateur: " . $conn->error;
            }
        }
    }
}

// Traiter la suppression d'un utilisateur
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $id = $_GET['delete'];
    
    // Empêcher la suppression de son propre compte
    if ($id == $_SESSION['user_id']) {
        $_SESSION['error_message'] = "Vous ne pouvez pas supprimer votre propre compte";
    } else {
        $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
        $stmt->bind_param("i", $id);
        
        if ($stmt->execute()) {
            $_SESSION['success_message'] = "Utilisateur supprimé avec succès";
        } else {
            $_SESSION['error_message'] = "Erreur lors de la suppression de l'utilisateur: " . $conn->error;
        }
    }
    
    header('Location: users.php');
    exit;
}

// Traiter la modification du rôle d'un utilisateur
if (isset($_GET['toggle_role']) && is_numeric($_GET['toggle_role'])) {
    $id = $_GET['toggle_role'];
    
    // Empêcher la modification de son propre rôle
    if ($id == $_SESSION['user_id']) {
        $_SESSION['error_message'] = "Vous ne pouvez pas modifier votre propre rôle";
    } else {
        // Récupérer le rôle actuel
        $stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        
        // Inverser le rôle
        $new_role = ($user['role'] === 'admin') ? 'user' : 'admin';
        
        $stmt = $conn->prepare("UPDATE users SET role = ? WHERE id = ?");
        $stmt->bind_param("si", $new_role, $id);
        
        if ($stmt->execute()) {
            $_SESSION['success_message'] = "Rôle de l'utilisateur modifié avec succès";
        } else {
            $_SESSION['error_message'] = "Erreur lors de la modification du rôle: " . $conn->error;
        }
    }
    
    header('Location: users.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Utilisateurs - Application Gestion des Achats</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="css/style.css">
  <style>
    body {
      padding-top: 56px;
    }
    .sidebar {
      width: 250px;
      position: fixed;
      top: 56px;
      bottom: 0;
      left: 0;
      z-index: 100;
      padding-top: 20px;
    }
    #main-content {
      margin-left: 250px;
      padding-top: 20px;
    }
    .nav-link {
      color: rgba(255,255,255,.75);
    }
    .nav-link:hover {
      color: #fff;
    }
    .user-info {
      border-bottom: 1px solid rgba(255,255,255,.1);
      padding-bottom: 15px;
    }
    @media (max-width: 768px) {
      .sidebar {
        width: 100%;
        position: relative;
        top: 0;
        padding-top: 0;
      }
      #main-content {
        margin-left: 0;
      }
    }
  </style>
</head>
<body>
  <?php include 'includes/navbar.php'; ?>

  <div class="d-flex">
    <?php include 'includes/sidebar.php'; ?>

    <!-- Contenu principal -->
    <div class="p-4 flex-grow-1" id="main-content">
      <div id="users" class="content-section">
        <h3 class="mb-4">Gestion des utilisateurs</h3>
        
        <?php if (isset($_SESSION['success_message'])): ?>
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error_message'])): ?>
          <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        <?php endif; ?>
        
        <div class="row mb-4">
          <div class="col-md-4">
            <div class="card shadow-sm">
              <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Ajouter un utilisateur</h5>
              </div>
              <div class="card-body">
                <form action="users.php" method="POST">
                  <div class="mb-3">
                    <label class="form-label">Nom d'utilisateur</label>
                    <input type="text" class="form-control" name="username" required>
                  </div>
                  <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-control" name="email" required>
                  </div>
                  <div class="mb-3">
                    <label class="form-label">Nom complet</label>
                    <input type="text" class="form-control" name="full_name" required>
                  </div>
                  <div class="mb-3">
                    <label class="form-label">Mot de passe</label>
                    <input type="password" class="form-control" name="password" required>
                  </div>
                  <div class="mb-3">
                    <label class="form-label">Rôle</label>
                    <select class="form-select" name="role" required>
                      <option value="user">Utilisateur</option>
                      <option value="admin">Administrateur</option>
                    </select>
                  </div>
                  <input type="hidden" name="add_user" value="1">
                  <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-plus-circle me-2"></i>Ajouter
                  </button>
                </form>
              </div>
            </div>
          </div>
          <div class="col-md-8">
            <div class="card shadow-sm">
              <div class="card-header bg-info text-white">
                <h5 class="mb-0">Liste des utilisateurs</h5>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>Nom d'utilisateur</th>
                        <th>Email</th>
                        <th>Nom complet</th>
                        <th>Rôle</th>
                        <th>Date de création</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <?php foreach ($users as $user): ?>
                      <tr>
                        <td><?php echo $user['id']; ?></td>
                        <td><?php echo $user['username']; ?></td>
                        <td><?php echo $user['email']; ?></td>
                        <td><?php echo $user['full_name']; ?></td>
                        <td>
                          <span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : 'success'; ?>">
                            <?php echo $user['role'] === 'admin' ? 'Administrateur' : 'Utilisateur'; ?>
                          </span>
                        </td>
                        <td><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></td>
                        <td>
                          <div class="btn-group">
                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                            <a href="users.php?toggle_role=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-primary" title="Changer le rôle">
                              <i class="bi bi-arrow-repeat"></i>
                            </a>
                            <a href="users.php?delete=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-danger" 
                               onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur?')" title="Supprimer">
                              <i class="bi bi-trash"></i>
                            </a>
                            <?php else: ?>
                            <button class="btn btn-sm btn-outline-secondary" disabled title="Vous ne pouvez pas modifier votre propre compte">
                              <i class="bi bi-lock"></i>
                            </button>
                            <?php endif; ?>
                          </div>
                        </td>
                      </tr>
                      <?php endforeach; ?>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Activer les tooltips Bootstrap
    document.addEventListener('DOMContentLoaded', function() {
      var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'))
      var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
      });
    });
  </script>
</body>
</html>
