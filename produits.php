<?php
// Initialiser la session
session_start();

// Connexion à la base de données
require_once 'config/database.php';

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Récupérer les produits
$stmt = $conn->prepare("SELECT p.*, s.name as supplier_name 
                        FROM products p 
                        LEFT JOIN suppliers s ON p.supplier_id = s.id 
                        ORDER BY p.name ASC");
$stmt->execute();
$products = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Récupérer les fournisseurs pour le formulaire
$stmt = $conn->prepare("SELECT id, name FROM suppliers WHERE status = 'Actif' ORDER BY name ASC");
$stmt->execute();
$suppliers = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Traiter l'ajout d'un nouveau produit
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_product'])) {
    $name = $_POST['name'];
    $price = $_POST['price'];
    $category = $_POST['category'];
    $stock = $_POST['stock'];
    $supplier_id = $_POST['supplier_id'];
    
    // Gestion de l'image
    $image = 'default.jpg'; // Image par défaut
    
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['image']['name'];
        $ext = pathinfo($filename, PATHINFO_EXTENSION);
        
        if (in_array(strtolower($ext), $allowed)) {
            $newname = uniqid() . '.' . $ext;
            $destination = 'uploads/' . $newname;
            
            if (move_uploaded_file($_FILES['image']['tmp_name'], $destination)) {
                $image = $newname;
            }
        }
    }
    
    $stmt = $conn->prepare("INSERT INTO products (name, price, category, stock, supplier_id, image) 
                           VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("sdsiis", $name, $price, $category, $stock, $supplier_id, $image);
    
    if ($stmt->execute()) {
        $_SESSION['success_message'] = "Produit ajouté avec succès";
    } else {
        $_SESSION['error_message'] = "Erreur lors de l'ajout du produit: " . $conn->error;
    }
    
    header('Location: produits.php');
    exit;
}

// Traiter la suppression d'un produit
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $id = $_GET['delete'];
    
    $stmt = $conn->prepare("DELETE FROM products WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        $_SESSION['success_message'] = "Produit supprimé avec succès";
    } else {
        $_SESSION['error_message'] = "Erreur lors de la suppression du produit: " . $conn->error;
    }
    
    header('Location: produits.php');
    exit;
}

// Traiter la création d'une commande
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_order'])) {
    $product_id = $_POST['product_id'];
    $quantity = $_POST['quantity'];
    $supplier_id = $_POST['supplier_id'];
    $delivery_date = $_POST['delivery_date'];
    $comments = $_POST['comments'] ?? '';
    
    // Rediriger vers la page des achats avec les paramètres
    header("Location: achats.php?new_order=1&product_id=$product_id&quantity=$quantity&supplier_id=$supplier_id&delivery_date=$delivery_date&comments=" . urlencode($comments));
    exit;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Produits - Application Gestion des Achats</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <?php include 'includes/navbar.php'; ?>

  <div class="d-flex">
    <?php include 'includes/sidebar.php'; ?>

    <!-- Contenu principal -->
    <div class="p-4 flex-grow-1" id="main-content">
      <div id="produits" class="content-section">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h3>Gestion des produits</h3>
          <button type="button" class="btn btn-sm btn-dark" data-bs-toggle="modal" data-bs-target="#addProductModal">
            <i class="bi bi-plus-circle me-1"></i>Ajouter un produit
          </button>
        </div>
        
        <?php if (isset($_SESSION['success_message'])): ?>
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error_message'])): ?>
          <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        <?php endif; ?>
        
        <div class="card shadow-sm">
          <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Liste des produits</h5>
            
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>Nom</th>
                    <th>Prix</th>
                    <th>Catégorie</th>
                    <th>Stock</th>
                    <th>Fournisseur</th>
                    <th>Image</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($products as $product): ?>
                  <tr class="<?php echo $product['stock'] < 5 ? 'table-warning' : ''; ?>">
                    <td><?php echo $product['name']; ?></td>
                    <td><?php echo $product['price']; ?> RMU</td>
                    <td><?php echo $product['category']; ?></td>
                    <td>
                      <span class="badge bg-<?php echo $product['stock'] < 5 ? 'danger' : 'success'; ?>">
                        <?php echo $product['stock']; ?>
                      </span>
                    </td>
                    <td><?php echo $product['supplier_name']; ?></td>
                    <td>
                      <button class="btn btn-sm btn-outline-info show-image-btn" data-bs-toggle="modal" data-bs-target="#imageModal" data-image="<?php echo $product['image']; ?>">
                        <i class="bi bi-image"></i> Voir
                      </button>
                    </td>
                    <td>
                      <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary edit-product-btn" data-bs-toggle="modal" data-bs-target="#editProductModal" 
                                data-id="<?php echo $product['id']; ?>"
                                data-name="<?php echo htmlspecialchars($product['name']); ?>"
                                data-price="<?php echo $product['price']; ?>"
                                data-category="<?php echo $product['category']; ?>"
                                data-stock="<?php echo $product['stock']; ?>"
                                data-supplier="<?php echo $product['supplier_id']; ?>">
                          <i class="bi bi-pencil"></i>
                        </button>
                        <a href="produits.php?delete=<?php echo $product['id']; ?>" class="btn btn-outline-danger" 
                           onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce produit?')">
                          <i class="bi bi-trash"></i>
                        </a>
                        <button class="btn btn-outline-success order-btn" data-bs-toggle="modal" data-bs-target="#createOrderModal"
                                data-id="<?php echo $product['id']; ?>"
                                data-supplier="<?php echo $product['supplier_id']; ?>">
                          <i class="bi bi-cart-plus"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal pour ajouter un produit -->
  <div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-dark text-white">
          <h5 class="modal-title" id="addProductModalLabel">Ajouter un produit</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form action="produits.php" method="POST" enctype="multipart/form-data">
            <div class="mb-3">
              <label class="form-label">Nom</label>
              <input type="text" class="form-control" name="name" required>
            </div>
            <div class="mb-3">
              <label class="form-label">Prix</label>
              <div class="input-group">
                <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                <span class="input-group-text">€</span>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Catégorie</label>
              <select class="form-select" name="category" required>
                <option value="" selected disabled>Sélectionner une catégorie</option>
                <option value="Informatique">Informatique</option>
                <option value="Mobilier">Mobilier</option>
                <option value="Papeterie">Papeterie</option>
                <option value="Fournitures">Fournitures</option>
                <option value="Autre">Autre</option>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Stock</label>
              <input type="number" class="form-control" name="stock" min="0" value="0" required>
            </div>
            <div class="mb-3">
              <label class="form-label">Fournisseur</label>
              <select class="form-select" name="supplier_id" required>
                <option value="" selected disabled>Sélectionner un fournisseur</option>
                <?php foreach ($suppliers as $supplier): ?>
                <option value="<?php echo $supplier['id']; ?>"><?php echo $supplier['name']; ?></option>
                <?php endforeach; ?>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Image</label>
              <input type="file" class="form-control" name="image">
              <div class="form-text">Formats acceptés: JPG, PNG, GIF</div>
            </div>
            <input type="hidden" name="add_product" value="1">
            <div class="text-end">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
              <button type="submit" class="btn btn-success">
                <i class="bi bi-plus-circle me-2"></i>Ajouter
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal pour modifier un produit -->
  <div class="modal fade" id="editProductModal" tabindex="-1" aria-labelledby="editProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-dark text-white">
          <h5 class="modal-title" id="editProductModalLabel">Modifier un produit</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form action="produits.php" method="POST" enctype="multipart/form-data">
            <div class="mb-3">
              <label class="form-label">Nom</label>
              <input type="text" class="form-control" name="name" id="edit-name" required>
            </div>
            <div class="mb-3">
              <label class="form-label">Prix</label>
              <div class="input-group">
                <input type="number" class="form-control" name="price" id="edit-price" step="0.01" min="0" required>
                <span class="input-group-text">€</span>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Catégorie</label>
              <select class="form-select" name="category" id="edit-category" required>
                <option value="Informatique">Informatique</option>
                <option value="Mobilier">Mobilier</option>
                <option value="Papeterie">Papeterie</option>
                <option value="Fournitures">Fournitures</option>
                <option value="Autre">Autre</option>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Stock</label>
              <input type="number" class="form-control" name="stock" id="edit-stock" min="0" required>
            </div>
            <div class="mb-3">
              <label class="form-label">Fournisseur</label>
              <select class="form-select" name="supplier_id" id="edit-supplier" required>
                <?php foreach ($suppliers as $supplier): ?>
                <option value="<?php echo $supplier['id']; ?>"><?php echo $supplier['name']; ?></option>
                <?php endforeach; ?>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Image</label>
              <input type="file" class="form-control" name="image">
              <div class="form-text">Laissez vide pour conserver l'image actuelle</div>
            </div>
            <input type="hidden" name="edit_product" value="1">
            <input type="hidden" name="product_id" id="edit-id">
            <div class="text-end">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-save me-2"></i>Enregistrer
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal pour créer un bon de commande -->
  <div class="modal fade" id="createOrderModal" tabindex="-1" aria-labelledby="createOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-dark text-white">
          <h5 class="modal-title" id="createOrderModalLabel">Créer un bon de commande</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form method="POST" action="produits.php">
            <div class="mb-3">
              <label class="form-label">Produit</label>
              <select class="form-select" name="product_id" id="order-product" required>
                <option value="" selected disabled>Sélectionner un produit</option>
                <?php foreach ($products as $product): ?>
                <option value="<?php echo $product['id']; ?>"><?php echo $product['name']; ?> (<?php echo $product['price']; ?> €)</option>
                <?php endforeach; ?>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Quantité</label>
              <input type="number" class="form-control" name="quantity" min="1" value="1" required>
            </div>
            <div class="mb-3">
              <label class="form-label">Fournisseur</label>
              <select class="form-select" name="supplier_id" id="order-supplier" required>
                <option value="" selected disabled>Sélectionner un fournisseur</option>
                <?php foreach ($suppliers as $supplier): ?>
                <option value="<?php echo $supplier['id']; ?>"><?php echo $supplier['name']; ?></option>
                <?php endforeach; ?>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Date de livraison souhaitée</label>
              <input type="date" class="form-control" name="delivery_date" required>
            </div>
            <div class="mb-3">
              <label class="form-label">Commentaires</label>
              <textarea class="form-control" name="comments" rows="2"></textarea>
            </div>
            <input type="hidden" name="create_order" value="1">
            <div class="text-end">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
              <button type="submit" class="btn btn-success">
                <i class="bi bi-plus-circle me-2"></i>Créer la commande
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal pour afficher l'image du produit -->
  <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="imageModalLabel">Image du produit</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body text-center">
          <img id="productImage" src="" class="img-fluid" alt="Image du produit">
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Gestion de l'affichage des images
      const imageModal = document.getElementById('imageModal');
      const productImage = document.getElementById('productImage');
      
      document.querySelectorAll('.show-image-btn').forEach(button => {
        button.addEventListener('click', function() {
          const imageUrl = this.getAttribute('data-image');
          productImage.src = 'uploads/' + imageUrl;
        });
      });
    });
  </script>
</body>
</html>

