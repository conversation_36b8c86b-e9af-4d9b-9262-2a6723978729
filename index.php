<?php
// Initialiser la session
session_start();

// Connexion à la base de données
require_once 'config/database.php';

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Récupérer les données de l'utilisateur
$user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();

// Récupérer quelques produits récents
$stmt = $conn->prepare("SELECT * FROM products ORDER BY id DESC LIMIT 4");
$stmt->execute();
$recent_products = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Accueil - Application Gestion des Achats</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <?php include 'includes/navbar.php'; ?>

  <div class="d-flex">
    <?php include 'includes/sidebar.php'; ?>

    <!-- Contenu principal -->
    <div class="p-4 flex-grow-1" id="main-content">
      <div id="accueil" class="content-section">
        <!-- En-tête de bienvenue -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-body p-4">
                <h2 class="mb-3">Bienvenue, <?php echo $user['full_name']; ?> !</h2>
                <p class="lead">Bienvenue dans l'application de Gestion des Achats. Utilisez le menu de gauche pour naviguer entre les différentes sections.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Accès rapides -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-white">
                <h5 class="mb-0">Accès rapides</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3 mb-3">
                    <a href="produits.php" class="card h-100 border-0 shadow-sm text-decoration-none">
                      <div class="card-body text-center">
                        <i class="bi bi-box-seam text-primary fs-1 mb-3"></i>
                        <h5 class="card-title">Produits</h5>
                        <p class="card-text text-muted">Gérer les produits</p>
                      </div>
                    </a>
                  </div>
                  <div class="col-md-3 mb-3">
                    <a href="fournisseurs.php" class="card h-100 border-0 shadow-sm text-decoration-none">
                      <div class="card-body text-center">
                        <i class="bi bi-people text-info fs-1 mb-3"></i>
                        <h5 class="card-title">Fournisseurs</h5>
                        <p class="card-text text-muted">Gérer les fournisseurs</p>
                      </div>
                    </a>
                  </div>
                  <div class="col-md-3 mb-3">
                    <a href="achats.php" class="card h-100 border-0 shadow-sm text-decoration-none">
                      <div class="card-body text-center">
                        <i class="bi bi-cart-check text-success fs-1 mb-3"></i>
                        <h5 class="card-title">Achats</h5>
                        <p class="card-text text-muted">Gérer les commandes</p>
                      </div>
                    </a>
                  </div>
                  <?php if ($_SESSION['role'] === 'admin'): ?>
                  <div class="col-md-3 mb-3">
                    <a href="users.php" class="card h-100 border-0 shadow-sm text-decoration-none">
                      <div class="card-body text-center">
                        <i class="bi bi-person-badge text-warning fs-1 mb-3"></i>
                        <h5 class="card-title">Utilisateurs</h5>
                        <p class="card-text text-muted">Gérer les utilisateurs</p>
                      </div>
                    </a>
                  </div>
                  <?php endif; ?>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Produits récents -->
        <div class="row">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Produits récents</h5>
                <button id="toggle-view" class="btn btn-sm btn-outline-primary">
                  <i class="bi bi-grid"></i> Changer la vue
                </button>
              </div>
              <div class="card-body">
                <!-- Vue en grille (par défaut) -->
                <div id="grid-view" class="row">
                  <?php foreach ($recent_products as $product): ?>
                  <div class="col-md-3 mb-3">
                    <div class="card h-100 border-0 shadow-sm">
                      <div class="card-body">
                        <h6 class="card-title"><?php echo $product['name']; ?></h6>
                        <p class="card-text text-muted"><?php echo $product['price']; ?> €</p>
                        <div class="d-flex justify-content-between align-items-center">
                          <span class="badge bg-<?php echo $product['stock'] < 5 ? 'danger' : 'success'; ?>"><?php echo $product['stock']; ?> en stock</span>
                          <a href="produits.php" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye"></i> Voir
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <?php endforeach; ?>
                </div>
                
                <!-- Vue en liste (cachée par défaut) -->
                <div id="list-view" class="d-none">
                  <div class="table-responsive">
                    <table class="table table-hover">
                      <thead class="table-light">
                        <tr>
                          <th>Nom</th>
                          <th>Prix</th>
                          <th>Stock</th>
                          <th>Image</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        <?php foreach ($recent_products as $product): ?>
                        <tr>
                          <td><?php echo $product['name']; ?></td>
                          <td><?php echo $product['price']; ?> €</td>
                          <td>
                            <span class="badge bg-<?php echo $product['stock'] < 5 ? 'danger' : 'success'; ?>">
                              <?php echo $product['stock']; ?> en stock
                            </span>
                          </td>
                          <td>
                            <button class="btn btn-sm btn-outline-info show-image-btn" data-image="<?php echo $product['image']; ?>">
                              <i class="bi bi-image"></i> Voir l'image
                            </button>
                          </td>
                          <td>
                            <a href="produits.php" class="btn btn-sm btn-outline-primary">
                              <i class="bi bi-eye"></i> Détails
                            </a>
                          </td>
                        </tr>
                        <?php endforeach; ?>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal pour afficher l'image du produit -->
  <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="imageModalLabel">Image du produit</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body text-center">
          <img id="productImage" src="" class="img-fluid" alt="Image du produit">
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Gestion du changement de vue (grille/liste)
      const toggleViewBtn = document.getElementById('toggle-view');
      const gridView = document.getElementById('grid-view');
      const listView = document.getElementById('list-view');
      
      toggleViewBtn.addEventListener('click', function() {
        if (gridView.classList.contains('d-none')) {
          // Passer à la vue en grille
          gridView.classList.remove('d-none');
          listView.classList.add('d-none');
          toggleViewBtn.innerHTML = '<i class="bi bi-list"></i> Vue liste';
        } else {
          // Passer à la vue en liste
          gridView.classList.add('d-none');
          listView.classList.remove('d-none');
          toggleViewBtn.innerHTML = '<i class="bi bi-grid"></i> Vue grille';
        }
      });
      
      // Gestion de l'affichage des images
      const imageModal = new bootstrap.Modal(document.getElementById('imageModal'));
      const productImage = document.getElementById('productImage');
      
      document.querySelectorAll('.show-image-btn').forEach(button => {
        button.addEventListener('click', function() {
          const imageUrl = this.getAttribute('data-image');
          productImage.src = imageUrl;
          imageModal.show();
        });
      });
    });
  </script>
</body>
</html>

