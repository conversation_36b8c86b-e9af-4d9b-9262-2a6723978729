<div class="sidebar bg-dark">
  <div class="p-3">
    <div class="user-info mb-3">
      <div class="d-flex align-items-center">
        <div class="user-avatar me-3">
          <i class="bi bi-person-circle fs-1 text-light"></i>
        </div>
        <div class="user-details">
          <h6 class="user-name mb-0 text-light"><?php echo $_SESSION['username']; ?></h6>
          <small class="text-muted"><?php echo ($_SESSION['role'] === 'admin') ? 'Administrateur' : 'Utilisateur'; ?></small>
        </div>
      </div>
    </div>
    <ul class="nav flex-column">
      <li class="nav-item">
        <a class="nav-link" href="index.php">
          <i class="bi bi-house-door me-2"></i>
          <span class="menu-text">Accueil</span>
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="produits.php">
          <i class="bi bi-box-seam me-2"></i>
          <span class="menu-text">Produits</span>
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="fournisseurs.php">
          <i class="bi bi-people me-2"></i>
          <span class="menu-text">Fournisseurs</span>
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="achats.php">
          <i class="bi bi-cart-check me-2"></i>
          <span class="menu-text">Achats</span>
        </a>
      </li>
      <?php if ($_SESSION['role'] === 'admin'): ?>
      <li class="nav-item">
        <a class="nav-link" href="users.php">
          <i class="bi bi-person-badge me-2"></i>
          <span class="menu-text">Utilisateurs</span>
        </a>
      </li>
      <?php endif; ?>
      <li class="nav-item mt-3">
        <a class="nav-link" href="logout.php">
          <i class="bi bi-box-arrow-right me-2"></i>
          <span class="menu-text">Déconnexion</span>
        </a>
      </li>
    </ul>
  </div>
</div>
