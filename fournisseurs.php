<?php
// Initialiser la session
session_start();

// Connexion à la base de données
require_once 'config/database.php';

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Récupérer les fournisseurs
$stmt = $conn->prepare("SELECT * FROM suppliers ORDER BY name ASC");
$stmt->execute();
$suppliers = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Traiter l'ajout d'un nouveau fournisseur
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_supplier'])) {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $products = $_POST['products'];
    $status = 'Actif';
    
    $stmt = $conn->prepare("INSERT INTO suppliers (name, contact, phone, products, status) 
                           VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("sssss", $name, $email, $phone, $products, $status);
    
    if ($stmt->execute()) {
        $_SESSION['success_message'] = "Fournisseur ajouté avec succès";
        header('Location: fournisseurs.php');
        exit;
    } else {
        $_SESSION['error_message'] = "Erreur lors de l'ajout du fournisseur: " . $conn->error;
    }
}

// Traiter la modification d'un fournisseur
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_supplier'])) {
    $id = $_POST['supplier_id'];
    $name = $_POST['name'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $products = $_POST['products'];
    $status = $_POST['status'];
    
    $stmt = $conn->prepare("UPDATE suppliers SET name = ?, contact = ?, phone = ?, products = ?, status = ? WHERE id = ?");
    $stmt->bind_param("sssssi", $name, $email, $phone, $products, $status, $id);
    
    if ($stmt->execute()) {
        $_SESSION['success_message'] = "Fournisseur modifié avec succès";
        header('Location: fournisseurs.php');
        exit;
    } else {
        $_SESSION['error_message'] = "Erreur lors de la modification du fournisseur: " . $conn->error;
    }
}

// Traiter la suppression d'un fournisseur
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $id = $_GET['delete'];
    
    // Vérifier si le fournisseur a des produits associés
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM products WHERE supplier_id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];
    
    if ($count > 0) {
        $_SESSION['error_message'] = "Impossible de supprimer ce fournisseur car il a des produits associés";
    } else {
        $stmt = $conn->prepare("DELETE FROM suppliers WHERE id = ?");
        $stmt->bind_param("i", $id);
        
        if ($stmt->execute()) {
            $_SESSION['success_message'] = "Fournisseur supprimé avec succès";
        } else {
            $_SESSION['error_message'] = "Erreur lors de la suppression du fournisseur: " . $conn->error;
        }
    }
    
    header('Location: fournisseurs.php');
    exit;
}

// Traiter le changement de statut d'un fournisseur
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    $id = $_GET['toggle_status'];
    
    // Récupérer le statut actuel
    $stmt = $conn->prepare("SELECT status FROM suppliers WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $supplier = $result->fetch_assoc();
    
    // Inverser le statut
    $new_status = ($supplier['status'] === 'Actif') ? 'Inactif' : 'Actif';
    
    $stmt = $conn->prepare("UPDATE suppliers SET status = ? WHERE id = ?");
    $stmt->bind_param("si", $new_status, $id);
    
    if ($stmt->execute()) {
        $_SESSION['success_message'] = "Statut du fournisseur modifié avec succès";
    } else {
        $_SESSION['error_message'] = "Erreur lors de la modification du statut: " . $conn->error;
    }
    
    header('Location: fournisseurs.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Fournisseurs - Application Gestion des Achats</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <?php include 'includes/navbar.php'; ?>

  <div class="d-flex">
    <?php include 'includes/sidebar.php'; ?>

    <!-- Contenu principal -->
    <div class="p-4 flex-grow-1" id="main-content">
      <div id="fournisseurs" class="content-section">
        <h3 class="mb-4">Gestion des fournisseurs</h3>
        
        <?php if (isset($_SESSION['success_message'])): ?>
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error_message'])): ?>
          <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        <?php endif; ?>
        
        <div class="row mb-4">
          <div class="col-md-4">
            <div class="card shadow-sm">
              <div class="card-header bg-dark text-white">
                <h5 class="mb-0">Ajouter un fournisseur</h5>
              </div>
              <div class="card-body">
                <form action="fournisseurs.php" method="POST">
                  <div class="mb-3">
                    <label class="form-label">Nom</label>
                    <input type="text" class="form-control" name="name" required>
                  </div>
                  <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-control" name="email" required>
                  </div>
                  <div class="mb-3">
                    <label class="form-label">Téléphone</label>
                    <input type="tel" class="form-control" name="phone" required>
                  </div>
                  <div class="mb-3">
                    <label class="form-label">Produits fournis</label>
                    <textarea class="form-control" name="products" rows="3" required></textarea>
                  </div>
                  <input type="hidden" name="add_supplier" value="1">
                  <button type="submit" class="btn btn-dark w-100">
                    <i class="bi bi-plus-circle me-2"></i>Ajouter
                  </button>
                </form>
              </div>
            </div>
          </div>
          <div class="col-md-8">
            <div class="card shadow-sm">
              <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Liste des fournisseurs</h5>
                <div class="d-flex align-items-center">
                  <a href="export_suppliers.php?export_excel=1" class="btn btn-sm btn-success me-2" title="Exporter vers Excel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Exporter Excel
                  </a>
                  <div class="btn-group filter-buttons">
                    <button class="btn btn-sm btn-light filter-all">Tous</button>
                    <button class="btn btn-sm btn-outline-light filter-active">Actifs</button>
                  </div>
                </div>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead>
                      <tr>
                        <th>Nom</th>
                        <th>Contact</th>
                        <th>Téléphone</th>
                        <th>Produits</th>
                        <th>Statut</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <?php foreach ($suppliers as $supplier): ?>
                      <tr class="supplier-row <?php echo $supplier['status'] === 'Inactif' ? 'table-secondary' : ''; ?>">
                        <td><?php echo htmlspecialchars($supplier['name']); ?></td>
                        <td><?php echo htmlspecialchars($supplier['contact']); ?></td>
                        <td><?php echo htmlspecialchars($supplier['phone']); ?></td>
                        <td><?php echo htmlspecialchars($supplier['products']); ?></td>
                        <td>
                          <span class="badge bg-<?php echo $supplier['status'] === 'Actif' ? 'success' : 'secondary'; ?>">
                            <?php echo $supplier['status']; ?>
                          </span>
                        </td>
                        <td>
                          <div class="btn-group">
                            <a href="fournisseurs.php?toggle_status=<?php echo $supplier['id']; ?>" class="btn btn-sm btn-outline-primary" title="Changer le statut">
                              <i class="bi bi-toggle-<?php echo $supplier['status'] === 'Actif' ? 'on' : 'off'; ?>"></i>
                            </a>
                            <button class="btn btn-sm btn-outline-secondary edit-supplier-btn" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#editSupplierModal" 
                                    data-id="<?php echo $supplier['id']; ?>"
                                    data-name="<?php echo htmlspecialchars($supplier['name']); ?>"
                                    data-email="<?php echo htmlspecialchars($supplier['contact']); ?>"
                                    data-phone="<?php echo htmlspecialchars($supplier['phone']); ?>"
                                    data-products="<?php echo htmlspecialchars($supplier['products']); ?>"
                                    data-status="<?php echo $supplier['status']; ?>"
                                    title="Modifier">
                              <i class="bi bi-pencil"></i>
                            </button>
                            <a href="fournisseurs.php?delete=<?php echo $supplier['id']; ?>" class="btn btn-sm btn-outline-danger" 
                               onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce fournisseur?')" title="Supprimer">
                              <i class="bi bi-trash"></i>
                            </a>
                            <a href="supplier_details.php?id=<?php echo $supplier['id']; ?>" class="btn btn-sm btn-outline-info" title="Voir les détails">
                              <i class="bi bi-eye"></i>
                            </a>
                          </div>
                        </td>
                      </tr>
                      <?php endforeach; ?>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de modification de fournisseur -->
  <div class="modal fade" id="editSupplierModal" tabindex="-1" aria-labelledby="editSupplierModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="editSupplierModalLabel">Modifier un fournisseur</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form action="fournisseurs.php" method="POST">
            <div class="mb-3">
              <label for="edit_name" class="form-label">Nom</label>
              <input type="text" class="form-control" id="edit_name" name="name" required>
            </div>
            <div class="mb-3">
              <label for="edit_email" class="form-label">Email</label>
              <input type="email" class="form-control" id="edit_email" name="email" required>
            </div>
            <div class="mb-3">
              <label for="edit_phone" class="form-label">Téléphone</label>
              <input type="tel" class="form-control" id="edit_phone" name="phone" required>
            </div>
            <div class="mb-3">
              <label for="edit_products" class="form-label">Produits fournis</label>
              <textarea class="form-control" id="edit_products" name="products" rows="3" required></textarea>
            </div>
            <div class="mb-3">
              <label for="edit_status" class="form-label">Statut</label>
              <select class="form-select" id="edit_status" name="status" required>
                <option value="Actif">Actif</option>
                <option value="Inactif">Inactif</option>
              </select>
            </div>
            <input type="hidden" name="supplier_id" id="edit_supplier_id">
            <input type="hidden" name="edit_supplier" value="1">
            <button type="submit" class="btn btn-primary w-100">Enregistrer les modifications</button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Script pour le filtrage des fournisseurs
    document.addEventListener('DOMContentLoaded', function() {
      const filterAllBtn = document.querySelector('.filter-all');
      const filterActiveBtn = document.querySelector('.filter-active');
      const supplierRows = document.querySelectorAll('.supplier-row');
      
      filterAllBtn.addEventListener('click', function() {
        supplierRows.forEach(row => {
          row.style.display = '';
        });
        filterAllBtn.classList.add('btn-light');
        filterAllBtn.classList.remove('btn-outline-light');
        filterActiveBtn.classList.add('btn-outline-light');
        filterActiveBtn.classList.remove('btn-light');
      });
      
      filterActiveBtn.addEventListener('click', function() {
        supplierRows.forEach(row => {
          if (row.classList.contains('table-secondary')) {
            row.style.display = 'none';
          } else {
            row.style.display = '';
          }
        });
        filterActiveBtn.classList.add('btn-light');
        filterActiveBtn.classList.remove('btn-outline-light');
        filterAllBtn.classList.add('btn-outline-light');
        filterAllBtn.classList.remove('btn-light');
      });
      
      // Remplir le modal de modification avec les données du fournisseur
      const editButtons = document.querySelectorAll('.edit-supplier-btn');
      editButtons.forEach(button => {
        button.addEventListener('click', function() {
          const id = this.getAttribute('data-id');
          const name = this.getAttribute('data-name');
          const email = this.getAttribute('data-email');
          const phone = this.getAttribute('data-phone');
          const products = this.getAttribute('data-products');
          const status = this.getAttribute('data-status');
          
          document.getElementById('edit_supplier_id').value = id;
          document.getElementById('edit_name').value = name;
          document.getElementById('edit_email').value = email;
          document.getElementById('edit_phone').value = phone;
          document.getElementById('edit_products').value = products;
          document.getElementById('edit_status').value = status;
        });
      });
    });
  </script>
</body>
</html>





