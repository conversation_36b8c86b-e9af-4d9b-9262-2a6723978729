<?php
// Paramètres de connexion à la base de données
$host = '127.0.0.1'; // Utiliser l'adresse IP au lieu de 'localhost'
$username = 'root';  // Nom d'utilisateur par défaut de XAMPP
$password = '';      // Mot de passe par défaut de XAMPP (vide)
$database = 'gestion_achats';  // Nom de votre base de données
$port = 3306;        // Port MySQL par défaut

// Créer la connexion
try {
    $conn = new mysqli($host, $username, $password, $database, $port);

    // Vérifier la connexion
    if ($conn->connect_error) {
        die("Échec de la connexion : " . $conn->connect_error);
    }

    // Définir l'encodage des caractères
    $conn->set_charset("utf8");
} catch (Exception $e) {
    // Afficher un message d'erreur plus convivial
    die("Impossible de se connecter à la base de données. Veuillez vérifier que le serveur MySQL est en cours d'exécution et que les paramètres de connexion sont corrects.");
}
?>
